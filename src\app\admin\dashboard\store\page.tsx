"use client";

import { useState } from "react";
import CategoryList from "@/components/admin/store/CategoryList";
import CategoryForm from "@/components/admin/store/CategoryForm";
import ProductList from "@/components/admin/store/ProductList";
import ProductForm from "@/components/admin/store/ProductForm";
import GroupedProductForm from "@/components/admin/store/GroupedProductForm";
import CategoryProductsView from "@/components/admin/store/CategoryProductsView";
import MainCategoryList from "@/components/admin/store/MainCategoryList";
import MainCategoryForm from "@/components/admin/store/MainCategoryForm";
import MainCategoryCategoriesView from "@/components/admin/store/MainCategoryCategoriesView";
import AddCategoriesToMainCategory from "@/components/admin/store/AddCategoriesToMainCategory";
import { Category, MainCategory, ProductType, Product } from "@/services/api";

type FormMode = "add" | "edit" | null;
type ActiveView = "categories" | "products" | "categoryProducts" | "mainCategories" | "mainCategoryCategories" | "addCategoriesToMainCategory";
type ProductFormType = "simple" | "grouped";

const StorePage = () => {
  const [activeTab, setActiveTab] = useState<"categories" | "products" | "mainCategories">("categories");
  const [formMode, setFormMode] = useState<FormMode>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedMainCategoryId, setSelectedMainCategoryId] = useState<number | null>(null);
  const [activeView, setActiveView] = useState<ActiveView>("categories");
  const [productFormType, setProductFormType] = useState<ProductFormType>("simple");
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const handleAddNew = () => {
    setFormMode("add");
  };

  const handleCloseForm = () => {
    setFormMode(null);
  };

  const handleViewCategoryProducts = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
    setActiveView("categoryProducts");
  };

  const handleBackToCategories = () => {
    setSelectedCategoryId(null);
    setActiveView("categories");
  };

  const handleBackToProducts = () => {
    setActiveView("products");
  };

  const handleViewMainCategoryCategories = (mainCategoryId: number) => {
    setSelectedMainCategoryId(mainCategoryId);
    setActiveView("mainCategoryCategories");
  };

  const handleBackToMainCategories = () => {
    setSelectedMainCategoryId(null);
    setActiveView("mainCategories");
  };

  const handleAddCategoriesToMainCategory = () => {
    setActiveView("addCategoriesToMainCategory");
  };

  const handleTabChange = (tab: "categories" | "products" | "mainCategories") => {
    setActiveTab(tab);
    setActiveView(tab);
    setFormMode(null);
    setSelectedCategoryId(null);
    setSelectedMainCategoryId(null);
  };

  return (
    <div className="min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Store Management</h1>
        {activeTab === "categories" && activeView === "categories" && !formMode && (
          <button
            onClick={handleAddNew}
            className="bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition"
          >
            Add New Category
          </button>
        )}
        {activeTab === "mainCategories" && activeView === "mainCategories" && !formMode && (
          <button
            onClick={handleAddNew}
            className="bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition"
          >
            Add New Main Category
          </button>
        )}
        {activeTab === "products" && activeView === "products" && !formMode && (
          <div className="flex space-x-2">
            <div className="relative group">
              <button
                className="bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition"
              >
                Add New Product
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg overflow-hidden z-20 hidden group-hover:block">
                <div className="py-1">
                  <button
                    onClick={() => {
                      setProductFormType("simple");
                      handleAddNew();
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Simple Product
                  </button>
                  <button
                    onClick={() => {
                      setProductFormType("grouped");
                      handleAddNew();
                    }}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Grouped Product
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => handleTabChange("categories")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "categories"
                  ? "border-main-color text-main-color"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Categories
            </button>
            <button
              onClick={() => handleTabChange("mainCategories")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "mainCategories"
                  ? "border-main-color text-main-color"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Main Categories
            </button>
            <button
              onClick={() => handleTabChange("products")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "products"
                  ? "border-main-color text-main-color"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Products
            </button>
          </nav>
        </div>
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        {/* Categories Tab */}
        {activeTab === "categories" && (
          <>
            {activeView === "categories" && (
              <>
                {formMode ? (
                  <CategoryForm mode={formMode} onClose={handleCloseForm} />
                ) : (
                  <CategoryList
                    onEdit={() => setFormMode("edit")}
                    onViewProducts={handleViewCategoryProducts}
                  />
                )}
              </>
            )}
            {activeView === "categoryProducts" && selectedCategoryId && (
              <>
                {formMode ? (
                  productFormType === "simple" ? (
                    <ProductForm
                      mode={formMode}
                      onClose={handleCloseForm}
                      categoryId={selectedCategoryId}
                    />
                  ) : (
                    <GroupedProductForm
                      mode={formMode}
                      onClose={handleCloseForm}
                      categoryId={selectedCategoryId}
                    />
                  )
                ) : (
                  <CategoryProductsView
                    categoryId={selectedCategoryId}
                    onBack={handleBackToCategories}
                    onEdit={(isEdit = false, product) => {
                      // Check product type if provided
                      if (product && product.type === ProductType.GROUPED) {
                        setProductFormType("grouped");
                      } else {
                        setProductFormType("simple");
                      }
                      setFormMode(isEdit ? "edit" : "add");
                    }}
                    onAddGroupedProduct={() => {
                      setProductFormType("grouped");
                      setFormMode("add");
                    }}
                  />
                )}
              </>
            )}
          </>
        )}

        {/* Main Categories Tab */}
        {activeTab === "mainCategories" && (
          <>
            {activeView === "mainCategories" && (
              <>
                {formMode ? (
                  <MainCategoryForm mode={formMode} onClose={handleCloseForm} />
                ) : (
                  <MainCategoryList
                    onEdit={() => setFormMode("edit")}
                    onViewCategories={handleViewMainCategoryCategories}
                    onAddCategories={() => handleAddCategoriesToMainCategory()}
                  />
                )}
              </>
            )}
            {activeView === "mainCategoryCategories" && selectedMainCategoryId && (
              <MainCategoryCategoriesView
                mainCategoryId={selectedMainCategoryId}
                onBack={handleBackToMainCategories}
                onAddCategories={handleAddCategoriesToMainCategory}
              />
            )}
            {activeView === "addCategoriesToMainCategory" && (
              <AddCategoriesToMainCategory onClose={handleBackToMainCategories} />
            )}
          </>
        )}

        {/* Products Tab */}
        {activeTab === "products" && (
          <>
            {formMode ? (
              productFormType === "simple" ? (
                <ProductForm mode={formMode} onClose={handleCloseForm} categoryId={selectedCategoryId} />
              ) : (
                <GroupedProductForm mode={formMode} onClose={handleCloseForm} categoryId={selectedCategoryId} />
              )
            ) : (
              <ProductList onEdit={(product) => {
                // Check product type and set the appropriate form type
                if (product && product.type === ProductType.GROUPED) {
                  setProductFormType("grouped");
                } else {
                  setProductFormType("simple");
                }
                setFormMode("edit");
                if (product) {
                  setSelectedProduct(product);
                }
              }} />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default StorePage;
