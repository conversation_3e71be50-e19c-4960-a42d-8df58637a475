import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// POST handler for adding categories to a main category
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/main-categories/${id}/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to add categories to main category with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (POST /main-categories/${params.id}/categories):`, error);
    return NextResponse.json(
      { error: `Failed to add categories to main category with ID ${params.id}` },
      { status: 500 }
    );
  }
}
