import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Product Section by ID API - Base URL:', API_BASE_URL);
console.log('Product Section by ID API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// GET handler for fetching a product section by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`Fetching product section with ID ${id}...`);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      console.error(`Product section with ID ${id} not found: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Product section with ID ${id} not found` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Successfully fetched product section with ID ${id}`);
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (GET /product-sections/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to fetch product section with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// PATCH handler for updating a product section
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();
    console.log(`Updating product section with ID ${id}:`, body);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error(`Failed to update product section with ID ${id}: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to update product section with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Successfully updated product section with ID ${id}`);
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (PATCH /product-sections/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to update product section with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// DELETE handler for deleting a product section
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`Deleting product section with ID ${id}...`);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Failed to delete product section with ID ${id}: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to delete product section with ID ${id}` },
        { status: response.status }
      );
    }

    console.log(`Successfully deleted product section with ID ${id}`);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`API proxy error (DELETE /product-sections/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to delete product section with ID ${params.id}` },
      { status: 500 }
    );
  }
}
