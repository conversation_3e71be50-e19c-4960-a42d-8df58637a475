"use client";

import { useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { FiTrash2, FiMinus, FiPlus, FiShoppingCart, FiArrowRight } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";

const CartPage = () => {
  const router = useRouter();
  const { 
    items, 
    totalPrice, 
    counter, 
    isLoading, 
    removeItem, 
    updateItemQuantity, 
    getCart 
  } = useCartStore();

  // Load cart on component mount
  useEffect(() => {
    getCart();
  }, [getCart]);

  const handleQuantityChange = (itemId: string, currentQuantity: number, change: number) => {
    const newQuantity = currentQuantity + change;
    if (newQuantity > 0) {
      updateItemQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = () => {
    router.push("/checkout");
  };

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
      <h1 className="text-3xl font-medium mb-8">Shopping Cart</h1>

      {items.length === 0 ? (
        <div className="py-16 text-center">
          <div className="flex flex-col items-center justify-center">
            <FiShoppingCart size={64} className="text-gray-300 mb-4" />
            <h2 className="text-2xl font-medium text-gray-800 mb-2">Your cart is empty</h2>
            <p className="text-gray-500 mb-8">Looks like you haven't added any products to your cart yet.</p>
            <Link 
              href="/shop" 
              className="bg-main-color text-white py-3 px-6 rounded-md hover:bg-main-color/90 transition-colors inline-flex items-center gap-2"
            >
              Continue Shopping <FiArrowRight />
            </Link>
          </div>
        </div>
      ) : (
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Cart Items */}
          <div className="w-full lg:w-2/3">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="hidden md:grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b">
                <div className="col-span-6 font-medium">Product</div>
                <div className="col-span-2 font-medium text-center">Price</div>
                <div className="col-span-2 font-medium text-center">Quantity</div>
                <div className="col-span-2 font-medium text-right">Total</div>
              </div>

              {items.map((item) => (
                <div key={item.id} className="p-4 border-b last:border-b-0">
                  <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                    {/* Product */}
                    <div className="col-span-1 md:col-span-6 flex gap-4 items-center">
                      <div className="w-20 h-20 relative flex-shrink-0 bg-gray-100 rounded overflow-hidden">
                        {item.image ? (
                          <Image 
                            src={item.image} 
                            alt={item.name} 
                            fill 
                            sizes="80px"
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-gray-400">
                            No image
                          </div>
                        )}
                      </div>
                      
                      <div>
                        <h3 className="font-medium">{item.name}</h3>
                        <p className="text-sm text-gray-500">SKU: {item.sku}</p>
                        <button 
                          onClick={() => removeItem(item.id)}
                          className="text-red-500 hover:text-red-700 text-sm flex items-center gap-1 mt-2 md:hidden"
                          disabled={isLoading}
                        >
                          <FiTrash2 size={14} /> Remove
                        </button>
                      </div>
                    </div>

                    {/* Price */}
                    <div className="col-span-1 md:col-span-2 text-left md:text-center">
                      <div className="md:hidden text-sm text-gray-500 mb-1">Price:</div>
                      ${item.price.toFixed(2)}
                    </div>

                    {/* Quantity */}
                    <div className="col-span-1 md:col-span-2 flex md:justify-center">
                      <div className="md:hidden text-sm text-gray-500 mb-1 mr-4">Quantity:</div>
                      <div className="flex items-center border rounded-md">
                        <button 
                          onClick={() => handleQuantityChange(item.id, item.quantity, -1)}
                          className="p-2 text-gray-500 hover:text-main-color disabled:opacity-50"
                          disabled={item.quantity <= 1 || isLoading}
                        >
                          <FiMinus size={16} />
                        </button>
                        <span className="px-3 py-1">{item.quantity}</span>
                        <button 
                          onClick={() => handleQuantityChange(item.id, item.quantity, 1)}
                          className="p-2 text-gray-500 hover:text-main-color disabled:opacity-50"
                          disabled={item.quantity >= item.stockQuantity || isLoading}
                        >
                          <FiPlus size={16} />
                        </button>
                      </div>
                    </div>

                    {/* Total */}
                    <div className="col-span-1 md:col-span-2 flex justify-between md:justify-end items-center">
                      <div className="md:hidden text-sm text-gray-500">Total:</div>
                      <div className="font-medium">${(item.price * item.quantity).toFixed(2)}</div>
                      <button 
                        onClick={() => removeItem(item.id)}
                        className="text-gray-400 hover:text-red-500 p-1 hidden md:block"
                        disabled={isLoading}
                      >
                        <FiTrash2 size={18} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex justify-between">
              <Link 
                href="/shop" 
                className="text-main-color hover:underline flex items-center gap-2"
              >
                <FiArrowRight className="rotate-180" /> Continue Shopping
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="w-full lg:w-1/3">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-medium mb-4">Order Summary</h2>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal ({counter} items)</span>
                  <span>${totalPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>Calculated at checkout</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span>Calculated at checkout</span>
                </div>
              </div>
              
              <div className="h-px bg-gray-200 my-4"></div>
              
              <div className="flex justify-between mb-6">
                <span className="font-medium">Total</span>
                <span className="font-bold text-xl">${totalPrice.toFixed(2)}</span>
              </div>
              
              <button 
                onClick={handleCheckout}
                className="w-full py-3 px-6 bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors flex items-center justify-center gap-2 disabled:opacity-70"
                disabled={isLoading}
              >
                Proceed to Checkout <FiArrowRight />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CartPage;
