"use client";

import Image from "next/image";
import Link from "next/link";
import { FiArrowRight } from "react-icons/fi";
import { useEffect, useRef } from "react";

// Define the structure for featured category data
interface FeaturedCategory {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  slug: string;
  bgColor: string;
}

// Featured categories data with the provided descriptions
const featuredCategories: FeaturedCategory[] = [
  {
    id: "packaging",
    title: "Packaging",
    description: "Rest assured, our products are designed to preserve the integrity of your goods. Let's begin packing with confidence! Crafted from premium materials, our cosmetic containers exude luxury and reliability for businesses. We offer comprehensive packaging services across pharmaceuticals, healthcare, cosmetics, food and beverage, household, and commercial sectors.",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2024/02/cocojojo-packaging-banner-1.png",
    slug: "packaging",
    bgColor: "bg-amber-50"
  },
  {
    id: "contract-manufacturing",
    title: "Contract Manufacturing",
    description: "Our Contract Manufacturing services offer a science-led approach to bring your beauty brand's vision to life. Backed by our GMP certified, ISO certified, and USDA organic certified factors, we ensure the highest quality and safety standards throughout the formulation. With three distinct options, including Semi-Customization, Formula Re-Creation, and Custom Formulating, we cater to the diverse needs of growing and established brands.",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2023/09/Screenshot-2023-09-13-090551.png",
    slug: "contract-manufacturing",
    bgColor: "bg-blue-50"
  },
  {
    id: "labeling-filling",
    title: "Labeling & Filling Services",
    description: "Taking your products from the lab to the shelf, our Labeling and Filling services complete the journey to success. With our GMP certified, ISO certified, and USDA organic certified factors, you can trust in the integrity of our processes. Custom Filling relieves businesses from bottling hassles, offering standard and unique packaging options for premium presentation.",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2023/09/Screenshot-2023-09-13-090617.png",
    slug: "labeling-filling",
    bgColor: "bg-green-50"
  },
  {
    id: "private-labeling",
    title: "Private Labeling",
    description: "Our Private Labeling services lay a solid foundation for brands seeking to establish themselves as premium players in the industry. With our GMP certified, ISO certified, and USDA organic certified factors, you can confidently develop your exclusive line of high-quality beauty products. Backed by ethical and sustainable practices, our Cruelty-Free products cater to conscious consumers.",
    imageUrl: "https://cdn-emlad.nitrocdn.com/zaHcvptYIRIaZiVZtPdkZAXwSDmXYXbC/assets/images/optimized/rev-5df82d4/cocojojo.com/wp-content/uploads/2023/08/AdobeStock_583313454.jpg",
    slug: "private-labeling",
    bgColor: "bg-rose-50"
  }
];

const FeaturedCategories = () => {
  // Create refs for each category section
  const categoryRefs = useRef<(HTMLDivElement | null)[]>([]);
  const headerRef = useRef<HTMLDivElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  // Set up simple scroll animation
  useEffect(() => {
    // Function to check if an element is in viewport
    const isInViewport = (element: HTMLElement) => {
      const rect = element.getBoundingClientRect();
      return (
        rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.75 &&
        rect.bottom >= 0
      );
    };

    // Function to handle scroll
    const handleScroll = () => {
      // Animate header
      if (headerRef.current && isInViewport(headerRef.current)) {
        headerRef.current.style.opacity = '1';
        headerRef.current.style.transform = 'translateY(0)';
      }

      // Animate categories
      categoryRefs.current.forEach((ref) => {
        if (ref && isInViewport(ref)) {
          ref.style.opacity = '1';
          ref.style.transform = 'translateX(0)';
        }
      });

      // Animate CTA
      if (ctaRef.current && isInViewport(ctaRef.current)) {
        ctaRef.current.style.opacity = '1';
        ctaRef.current.style.transform = 'translateY(0)';
      }
    };

    // Initial check
    setTimeout(handleScroll, 100);

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <div className="py-16">
      {/* Header Section */}
      <div
        ref={headerRef}
        className="mb-16 text-center"
        style={{
          opacity: '0',
          transform: 'translateY(20px)',
          transition: 'opacity 0.8s ease, transform 0.8s ease'
        }}
      >
        <span className="text-main-color font-medium text-sm uppercase tracking-wider">What We Offer</span>
        <h2 className="text-3xl md:text-4xl font-medium text-gray-800 mt-2 mb-4">Our Premium Services</h2>
        <div className="w-24 h-1 bg-main-color mx-auto mb-6"></div>
        <p className="text-gray-600 max-w-3xl mx-auto">
          Discover our comprehensive range of services designed to meet all your beauty and cosmetic product needs,
          from packaging to manufacturing and beyond.
        </p>
      </div>

      <div className="space-y-24">
        {featuredCategories.map((category, index) => {
          const isEven = index % 2 === 0;

          return (
            <div
              key={category.id}
              ref={el => { categoryRefs.current[index] = el; }}
              className={`${category.bgColor} rounded-2xl overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300`}
              style={{
                opacity: '0',
                transform: isEven ? 'translateX(50px)' : 'translateX(-50px)',
                transition: 'opacity 0.8s ease, transform 0.8s ease'
              }}
            >
              <div className={`flex flex-col ${isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center`}>
                {/* Image Section */}
                <div className="w-full lg:w-1/2 h-80 md:h-96 lg:h-[500px] relative overflow-hidden group">
                  <Image
                    src={category.imageUrl}
                    alt={category.title}
                    fill
                    priority={index < 2} // Load first two images with priority
                    className="object-cover transition-transform duration-700 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Content Section */}
                <div className="w-full lg:w-1/2 p-8 md:p-12 lg:p-16">
                  <h3 className="text-2xl md:text-3xl font-medium text-gray-800 mb-4">
                    {category.title}
                  </h3>
                  <div className="w-20 h-1 bg-main-color mb-6 transition-all duration-300 hover:w-32"></div>
                  <p className="text-gray-700 mb-8 leading-relaxed">
                    {category.description}
                  </p>
                  <Link
                    href={`/shop/${category.slug}`}
                    className="inline-flex items-center gap-2 bg-main-color hover:bg-main-color/90 text-white py-3 px-6 rounded-lg transition-all duration-300 hover:gap-3 hover:pl-7"
                  >
                    <span>Explore {category.title}</span>
                    <FiArrowRight className="transition-transform duration-300 group-hover:translate-x-1" />
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Call to Action Section */}
      <div
        ref={ctaRef}
        className="mt-24 bg-main-color/10 rounded-2xl p-8 md:p-12 lg:p-16 text-center"
        style={{
          opacity: '0',
          transform: 'translateY(20px)',
          transition: 'opacity 0.8s ease, transform 0.8s ease'
        }}
      >
        <h3 className="text-2xl md:text-3xl font-medium text-gray-800 mb-4">Ready to Get Started?</h3>
        <p className="text-gray-700 max-w-3xl mx-auto mb-8">
          Whether you&#39;re looking for packaging solutions, contract manufacturing, or private labeling services,
          we&#39;re here to help bring your vision to life with our expertise and premium quality.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link
            href="/shop"
            className="bg-main-color hover:bg-main-color/90 text-white py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            Explore All Products
          </Link>
          <Link
            href="/contact"
            className="bg-white border border-main-color text-main-color hover:bg-main-color/5 py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105"
          >
            Contact Us
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FeaturedCategories;
