"use client";

import { useState, useEffect } from "react";
import { useMainCategoryStore } from "@/hooks/useMainCategoryStore";
import { useCategoryStore } from "@/hooks/useCategoryStore";
import { FiAlertTriangle, FiArrowLeft } from "react-icons/fi";
import LoadingSpinner from "@/components/LoadingSpinner";

interface AddCategoriesToMainCategoryProps {
  onClose: () => void;
}

const AddCategoriesToMainCategory = ({ onClose }: AddCategoriesToMainCategoryProps) => {
  const { selectedMainCategory, addCategoriesToMainCategory, error: storeError, clearError } = useMainCategoryStore();
  const { categories, fetchCategories } = useCategoryStore();

  const [selectedCategoryIds, setSelectedCategoryIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [availableCategories, setAvailableCategories] = useState<typeof categories>([]);

  // Clear errors when component mounts and fetch categories
  useEffect(() => {
    clearError();
    setFormError(null);
    fetchCategories();
  }, [clearError, fetchCategories]);

  // Filter out categories that are already in the main category
  useEffect(() => {
    if (selectedMainCategory && categories.length > 0) {
      const existingCategoryIds = selectedMainCategory.categories?.map(cat => cat.id!) || [];
      const filteredCategories = categories.filter(cat => !existingCategoryIds.includes(cat.id!));
      setAvailableCategories(filteredCategories);
    }
  }, [selectedMainCategory, categories]);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selected = Array.from(e.target.selectedOptions).map(option => parseInt(option.value));
    setSelectedCategoryIds(selected);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setFormError(null);
    clearError();

    if (selectedCategoryIds.length === 0) {
      setFormError("Please select at least one category");
      return;
    }

    setIsSubmitting(true);

    try {
      if (selectedMainCategory?.id) {
        await addCategoriesToMainCategory(selectedMainCategory.id, selectedCategoryIds);
        onClose();
      } else {
        setFormError("No main category selected");
      }
    } catch (err) {
      setFormError(err instanceof Error ? err.message : "An error occurred");
      console.error("Form submission error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!selectedMainCategory) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No main category selected.</p>
        <button
          onClick={onClose}
          className="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <button
          onClick={onClose}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
          title="Go Back"
        >
          <FiArrowLeft size={20} />
        </button>
        <h2 className="text-xl font-semibold">
          Add Categories to &quot;{selectedMainCategory.name}&quot;
        </h2>
      </div>

      {(formError || storeError) && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 flex items-start">
          <FiAlertTriangle className="mr-2 mt-0.5 flex-shrink-0" />
          <div>{formError || storeError}</div>
        </div>
      )}

      {availableCategories.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500">
            No available categories to add. All categories are already associated with this main category.
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="categoryIds" className="block text-sm font-medium text-gray-700 mb-1">
                Select Categories to Add *
              </label>
              <select
                id="categoryIds"
                name="categoryIds"
                multiple
                value={selectedCategoryIds.map(String)}
                onChange={handleCategoryChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                size={10}
              >
                {availableCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                Hold Ctrl (or Cmd) to select multiple categories
              </p>
            </div>
          </div>

          <div className="mt-8 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 flex items-center"
              disabled={isSubmitting || selectedCategoryIds.length === 0}
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Adding Categories...
                </>
              ) : (
                "Add Categories"
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default AddCategoriesToMainCategory;



