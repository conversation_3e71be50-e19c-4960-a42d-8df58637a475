"use client";

import { useState, useEffect } from "react";
import { useMainCategoryStore } from "@/hooks/useMainCategoryStore";
import { useCategoryStore } from "@/hooks/useCategoryStore";
import { MainCategory } from "@/services/api";
import { FiAlertTriangle } from "react-icons/fi";
import LoadingSpinner from "@/components/LoadingSpinner";

interface MainCategoryFormProps {
  mode: "add" | "edit";
  onClose: () => void;
}

const MainCategoryForm = ({ mode, onClose }: MainCategoryFormProps) => {
  const { selectedMainCategory, createMainCategory, updateMainCategory, setSelectedMainCategory, error: storeError, clearError } = useMainCategoryStore();
  const { categories, fetchCategories } = useCategoryStore();

  const [formData, setFormData] = useState<MainCategory>({
    name: "",
    slug: "",
    categoryIds: [],
    imageUrl: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [slugEdited, setSlugEdited] = useState(false);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setFormError(null);
    fetchCategories();
  }, [clearError, fetchCategories]);

  // Populate form data when editing
  useEffect(() => {
    if (mode === "edit" && selectedMainCategory) {
      setFormData({
        name: selectedMainCategory.name,
        slug: selectedMainCategory.slug,
        categoryIds: selectedMainCategory.categories?.map(cat => cat.id!) || selectedMainCategory.categoryIds || [],
        imageUrl: selectedMainCategory.imageUrl || "",
      });
      setSlugEdited(true); // Assume slug is already edited in edit mode
    }
  }, [mode, selectedMainCategory]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Mark slug as edited if the user changes it directly
    if (name === "slug") {
      setSlugEdited(true);
    }

    // Auto-generate slug from name if it's a new category or if the slug field hasn't been manually edited
    if (name === "name" && (mode === "add" || !slugEdited)) {
      setFormData({
        ...formData,
        [name]: value,
        slug: value.toLowerCase().replace(/\s+/g, "-").replace(/[^a-z0-9-]/g, ""),
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions).map(option => parseInt(option.value));
    setFormData({
      ...formData,
      categoryIds: selectedOptions,
    });
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      setFormError("Name is required");
      return false;
    }

    if (!formData.slug.trim()) {
      setFormError("Slug is required");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setFormError(null);
    clearError();

    // Validate form
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (mode === "add") {
        await createMainCategory(formData);
      } else if (mode === "edit" && selectedMainCategory?.id) {
        await updateMainCategory(selectedMainCategory.id, formData);
      }

      // Reset form and close
      setFormData({ name: "", slug: "", categoryIds: [], imageUrl: "" });
      setSelectedMainCategory(null);
      onClose();
    } catch (err) {
      setFormError(err instanceof Error ? err.message : "An error occurred");
      console.error("Form submission error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">
        {mode === "add" ? "Add New Main Category" : "Edit Main Category"}
      </h2>

      {(formError || storeError) && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 flex items-start">
          <FiAlertTriangle className="mr-2 mt-0.5 flex-shrink-0" />
          <div>{formError || storeError}</div>
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
              placeholder="e.g. Women's Fashion"
              required
            />
          </div>

          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
              Slug *
            </label>
            <input
              type="text"
              id="slug"
              name="slug"
              value={formData.slug}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
              placeholder="e.g. womens-fashion"
              required
            />
            <p className="mt-1 text-xs text-gray-500">
              Used in URLs. Only lowercase letters, numbers, and hyphens.
            </p>
          </div>

          <div>
            <label htmlFor="categoryIds" className="block text-sm font-medium text-gray-700 mb-1">
              Categories
            </label>
            <select
              id="categoryIds"
              name="categoryIds"
              multiple
              value={formData.categoryIds?.map(String) || []}
              onChange={handleCategoryChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
              size={5}
            >
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              Hold Ctrl (or Cmd) to select multiple categories
            </p>
          </div>

          <div>
            <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-1">
              Image URL
            </label>
            <input
              type="text"
              id="imageUrl"
              name="imageUrl"
              value={formData.imageUrl}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
              placeholder="https://example.com/image.jpg"
            />
            <p className="mt-1 text-xs text-gray-500">
              URL to an image for this main category. Leave empty for no image.
            </p>
          </div>
        </div>

        <div className="mt-8 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                {mode === "add" ? "Creating..." : "Updating..."}
              </>
            ) : (
              <>{mode === "add" ? "Create Main Category" : "Update Main Category"}</>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default MainCategoryForm;
