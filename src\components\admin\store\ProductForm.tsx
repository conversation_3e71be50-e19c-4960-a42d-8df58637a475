"use client";

import { useState, useEffect } from "react";
import { useProductStore } from "@/hooks/useProductStore";
import { useCategoryStore } from "@/hooks/useCategoryStore";
import {
  Product,
  ProductType,
  StockStatus,
  TaxStatus,
  TaxClass,
  AccessLevel,
  ProductImage,
  ProductListing,
  GroupedProductUpdateData
} from "@/services/api";
import { FiAlertTriangle, FiPlus, FiX } from "react-icons/fi";
import RichTextEditor from "../common/RichTextEditor";
import SeoEvaluator from "../common/SeoEvaluator";
import ListingsManager from "./ListingsManager";

interface ProductFormProps {
  mode: "add" | "edit";
  onClose: () => void;
  categoryId?: number | null;
}

interface TagObject {
  id?: number;
  name: string;
  slug?: string;
}

type ProductFormData = Omit<Product, 'tags'> & {
  tags: Array<string | TagObject>;
  listings?: ProductListing[];
};

// Helper function to format dates for the API
const formatDateForAPI = (dateString: string): string | null => {
  if (!dateString) return null;

  try {
    // If the date already has seconds and timezone, return it as is
    if (dateString.includes('Z') || dateString.includes('+')) {
      return dateString;
    }

    // Parse the date and format it as ISO string with timezone
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return null; // Invalid date
    }

    // Return full ISO string with timezone
    return date.toISOString();
  } catch (error) {
    console.error("Error formatting date:", error);
    return null;
  }
};

const ProductForm = ({ mode, onClose, categoryId }: ProductFormProps) => {
  const { selectedProduct, createProduct, createProductForCategory, updateProduct, updateGroupedProduct, setSelectedProduct, clearError } = useProductStore();
  const { categories, fetchCategories } = useCategoryStore();

  const [formData, setFormData] = useState<ProductFormData>({
    sku: "",
    name: "",
    description: "",
    shortDescription: "",
    price: "",
    salePrice: "",
    saleStart: "",
    saleEnd: "",
    stockQuantity: 0,
    stockStatus: StockStatus.IN_STOCK,
    taxStatus: TaxStatus.TAXABLE,
    taxClass: TaxClass.STANDARD,
    type: ProductType.SIMPLE, // Always set to SIMPLE
    access: AccessLevel.PUBLIC,
    password: "",
    tags: [],
    images: [],
    categoryIds: categoryId ? [categoryId] : [],
    listings: [],
  });

  const [deleteListingIds, setDeleteListingIds] = useState<number[]>([]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState("");
  const [tagInput, setTagInput] = useState("");

  // Load categories if not already loaded
  useEffect(() => {
    if (categories.length === 0) {
      fetchCategories();
    }
  }, [categories.length, fetchCategories]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
    setFormError(null);
  }, [clearError]);

  // Debug log for categoryIds
  useEffect(() => {
    console.log("Current formData.categoryIds:", formData.categoryIds);
  }, [formData.categoryIds]);

  // Load selected product data when in edit mode
  useEffect(() => {
    if (mode === "edit" && selectedProduct) {
      // Extract categoryIds from categories array
      let categoryIds: number[] = [];

      if (selectedProduct.categories && selectedProduct.categories.length > 0) {
        // If we have categories array, extract the IDs
        categoryIds = selectedProduct.categories
          .filter(cat => cat && cat.id !== undefined)
          .map(cat => cat.id as number);

        console.log("Extracted categoryIds from categories:", categoryIds);
      } else if (selectedProduct.categoryIds && selectedProduct.categoryIds.length > 0) {
        // If we have categoryIds array, use it directly
        categoryIds = selectedProduct.categoryIds;
        console.log("Using existing categoryIds:", categoryIds);
      }

      // Ensure all fields are included, even if they're empty or undefined
      const updatedFormData = {
        id: selectedProduct.id,
        sku: selectedProduct.sku || "",
        name: selectedProduct.name || "",
        description: selectedProduct.description || "",
        shortDescription: selectedProduct.shortDescription || "",
        price: selectedProduct.price || "",
        salePrice: selectedProduct.salePrice || "",
        saleStart: selectedProduct.saleStart || "",
        saleEnd: selectedProduct.saleEnd || "",
        stockQuantity: selectedProduct.stockQuantity || 0,
        stockStatus: selectedProduct.stockStatus || StockStatus.IN_STOCK,
        taxStatus: selectedProduct.taxStatus || TaxStatus.TAXABLE,
        taxClass: selectedProduct.taxClass || TaxClass.STANDARD,
        type: selectedProduct.type || ProductType.SIMPLE,
        images: selectedProduct.images || [],
        categoryIds: categoryIds,
        categories: selectedProduct.categories || [],
        tags: selectedProduct.tags || [], // Ensure tags are properly loaded
        access: selectedProduct.access || AccessLevel.PUBLIC,
        password: selectedProduct.password || "",
        createdAt: selectedProduct.createdAt,
        updatedAt: selectedProduct.updatedAt,
        listings: selectedProduct.listings || [] // Include listings if available
      };

      setFormData(updatedFormData);

      console.log("Loaded product data for editing:", selectedProduct);
      console.log("Form data with categories and tags:", updatedFormData);
    } else if (mode === "add" && categoryId) {
      // For new products in a specific category, pre-select that category
      setFormData(prev => ({
        ...prev,
        categoryIds: [categoryId],
      }));
    }
  }, [mode, selectedProduct, categoryId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    if (name === "stockQuantity") {
      setFormData({
        ...formData,
        [name]: parseInt(value) || 0,
      });
    } else if (name === "saleStart" || name === "saleEnd") {
      // Store the date value as is in the form state
      // It will be formatted properly when submitting to the API
      setFormData({
        ...formData,
        [name]: value,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  // Handle rich text editor changes
  const handleRichTextChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOptions = Array.from(e.target.selectedOptions);
    const categoryIds = selectedOptions.map(option => parseInt(option.value));

    setFormData({
      ...formData,
      categoryIds,
    });
  };

  const handleAddImage = () => {
    if (!imageUrl.trim()) return;

    const newImage: ProductImage = {
      url: imageUrl,
      position: formData.images.length,
    };

    setFormData({
      ...formData,
      images: [...formData.images, newImage],
    });

    setImageUrl("");
  };

  const handleRemoveImage = (index: number) => {
    const updatedImages = formData.images.filter((_, i) => i !== index);

    // Update positions after removal
    const reorderedImages = updatedImages.map((img, i) => ({
      ...img,
      position: i,
    }));

    setFormData({
      ...formData,
      images: reorderedImages,
    });
  };

  // Handle adding tags
  const handleAddTag = () => {
    if (!tagInput.trim()) return;

    // Don't add duplicate tags
    if (formData.tags?.includes(tagInput.trim())) {
      setTagInput("");
      return;
    }

    setFormData({
      ...formData,
      tags: [...(formData.tags || []), tagInput.trim()]
    });

    setTagInput("");
  };

  // Handle removing tags
  const handleRemoveTag = (tagToRemove: string | TagObject) => {
    const updatedTags = formData.tags.filter(tag =>
      typeof tag === 'string'
        ? tag !== tagToRemove
        : typeof tagToRemove === 'string'
          ? tag.name !== tagToRemove
          : tag.id !== tagToRemove.id
    );

    setFormData({
      ...formData,
      tags: updatedTags,
    });
  };

  // Handle tag input keydown (add tag on Enter)
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle listings changes
  const handleListingsChange = (listings: ProductListing[], deleteIds?: number[]) => {
    setFormData({
      ...formData,
      listings
    });

    if (deleteIds && deleteIds.length > 0) {
      setDeleteListingIds(deleteIds);
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      setFormError("Product name is required");
      return false;
    }

    if (!formData.sku.trim()) {
      setFormError("SKU is required");
      return false;
    }

    if (!formData.price.trim()) {
      setFormError("Price is required");
      return false;
    }

    if (formData.images.length === 0) {
      setFormError("At least one image is required");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    clearError();
    setFormError(null);

    try {
      // Clean up the form data to remove empty optional fields
      const cleanedFormData: Partial<Product> = {};

      // Include required fields
      cleanedFormData.name = formData.name;
      cleanedFormData.sku = formData.sku;
      cleanedFormData.price = formData.price;
      cleanedFormData.type = formData.type;
      cleanedFormData.stockStatus = formData.stockStatus;
      cleanedFormData.taxStatus = formData.taxStatus;
      cleanedFormData.taxClass = formData.taxClass;
      cleanedFormData.images = formData.images;
      cleanedFormData.access = formData.access;

      // Convert tags to strings if they are objects
      if (formData.tags && formData.tags.length > 0) {
        cleanedFormData.tags = formData.tags.map(tag =>
          typeof tag === 'string' ? tag : tag.name
        );
      }

      // Only include password if access is PROTECTED
      if (formData.access === AccessLevel.PROTECTED && formData.password) {
        cleanedFormData.password = formData.password;
      }

      // Only include categoryIds if they exist
      if (formData.categoryIds && formData.categoryIds.length > 0) {
        cleanedFormData.categoryIds = formData.categoryIds;
      }

      // Only include non-empty optional fields
      if (formData.salePrice && formData.salePrice.trim() !== "") {
        cleanedFormData.salePrice = formData.salePrice;
      }

      // Handle date fields - format them properly for the API
      if (formData.saleStart && formData.saleStart.trim() !== "") {
        const formattedDate = formatDateForAPI(formData.saleStart);
        if (formattedDate) {
          cleanedFormData.saleStart = formattedDate;
        }
      }

      if (formData.saleEnd && formData.saleEnd.trim() !== "") {
        const formattedDate = formatDateForAPI(formData.saleEnd);
        if (formattedDate) {
          cleanedFormData.saleEnd = formattedDate;
        }
      }

      if (formData.shortDescription && formData.shortDescription.trim() !== "") {
        cleanedFormData.shortDescription = formData.shortDescription;
      }

      if (formData.description && formData.description.trim() !== "") {
        cleanedFormData.description = formData.description;
      }

      // Only include stockQuantity if it's not 0
      const stockQuantityValue = typeof formData.stockQuantity === 'string'
        ? parseInt(formData.stockQuantity) || 0
        : (formData.stockQuantity || 0);

      if (stockQuantityValue !== 0) {
        cleanedFormData.stockQuantity = stockQuantityValue;
      }

      // Include listings if they exist
      if (formData.listings && formData.listings.length > 0) {
        cleanedFormData.listings = formData.listings;
      }

      // Include deleteListingIds if they exist and we're in edit mode
      if (mode === "edit" && deleteListingIds.length > 0) {
        (cleanedFormData as any).deleteListingIds = deleteListingIds;
      }

      console.log(`ProductForm - Stock quantity value: ${stockQuantityValue}, included in request: ${stockQuantityValue !== 0}`);
      console.log(`ProductForm - Submitting form in ${mode} mode with cleaned data:`, cleanedFormData);

      let result = null;

      if (mode === "add") {
        if (categoryId) {
          // If a categoryId is provided, use the createProductForCategory function
          console.log(`ProductForm - Creating product for category ID ${categoryId}`);
          result = await createProductForCategory(categoryId, cleanedFormData);
          console.log(`ProductForm - Created product for category:`, result);
        } else {
          // Otherwise, use the regular createProduct function
          console.log(`ProductForm - Creating product`);
          result = await createProduct(cleanedFormData);
          console.log(`ProductForm - Created product:`, result);
        }
      } else if (mode === "edit" && selectedProduct?.id) {
        console.log(`ProductForm - Updating product with ID ${selectedProduct.id}`);
        
        // For grouped products, use the base update endpoint
        if (selectedProduct.type === ProductType.GROUPED) {
          const updateData: GroupedProductUpdateData = {
            name: cleanedFormData.name,
            sku: cleanedFormData.sku,
            description: cleanedFormData.description,
            shortDescription: cleanedFormData.shortDescription,
            price: cleanedFormData.price,
            salePrice: cleanedFormData.salePrice || undefined,
            saleStart: cleanedFormData.saleStart || undefined,
            saleEnd: cleanedFormData.saleEnd || undefined,
            stockQuantity: stockQuantityValue > 0 ? stockQuantityValue : 0,
            stockStatus: cleanedFormData.stockStatus,
            taxStatus: cleanedFormData.taxStatus,
            taxClass: cleanedFormData.taxClass,
            access: cleanedFormData.access,
            password: cleanedFormData.access === AccessLevel.PROTECTED ? cleanedFormData.password : undefined,
            categoryIds: cleanedFormData.categoryIds,
            tags: cleanedFormData.tags,
            images: cleanedFormData.images
          };
          result = await updateGroupedProduct(selectedProduct.id, updateData);
        } else {
          result = await updateProduct(selectedProduct.id, cleanedFormData);
        }
        console.log(`ProductForm - Updated product:`, result);
      }

      if (!result) {
        throw new Error("Failed to save product. No response received from server.");
      }

      // Reset form and close
      setFormData({
        sku: "",
        name: "",
        description: "",
        shortDescription: "",
        price: "",
        salePrice: "",
        saleStart: "",
        saleEnd: "",
        stockQuantity: 0,
        stockStatus: StockStatus.IN_STOCK,
        taxStatus: TaxStatus.TAXABLE,
        taxClass: TaxClass.STANDARD,
        type: ProductType.SIMPLE,
        access: AccessLevel.PUBLIC,
        password: "",
        tags: [],
        images: [],
        categoryIds: [],
        listings: [],
      });
      setDeleteListingIds([]);
      setSelectedProduct(null);
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setFormError(errorMessage);
      console.error("ProductForm - Form submission error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      sku: "",
      name: "",
      description: "",
      shortDescription: "",
      price: "",
      salePrice: "",
      saleStart: "",
      saleEnd: "",
      stockQuantity: 0,
      stockStatus: StockStatus.IN_STOCK,
      taxStatus: TaxStatus.TAXABLE,
      taxClass: TaxClass.STANDARD,
      type: ProductType.SIMPLE,
      access: AccessLevel.PUBLIC,
      password: "",
      tags: [],
      images: [],
      categoryIds: [],
      listings: [],
    });
    setSelectedProduct(null);
    setDeleteListingIds([]);
    clearError();
    setFormError(null);
    onClose();
  };

  // Type is always SIMPLE, no need for attribute-related functions

  return (
    <div className="max-w-4xl mx-auto">
      <h2 className="text-xl font-semibold mb-6">
        {mode === "add" ? "Add New Product" : "Edit Product"}
      </h2>

      {formError && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <FiAlertTriangle className="mr-2 flex-shrink-0" />
          <span>{formError}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Basic Information */}
          <div className="space-y-6">
            <h3 className="text-lg font-medium border-b pb-2">Basic Information</h3>

            <div className="mb-8">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <div className="flex items-center gap-2">
                <div className="flex-grow">
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                  />
                </div>
                {formData.name && formData.name.length > 3 && (
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 rounded-full bg-gray-100 border flex items-center justify-center relative">
                      <div className="text-sm font-bold" id="titleSeoScore">
                        {/* Score will be updated by JS */}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="mt-4">
                <SeoEvaluator
                  title={formData.name}
                  content={formData.name}
                  type="title"
                />
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-2">
                SKU *
              </label>
              <input
                type="text"
                id="sku"
                name="sku"
                value={formData.sku}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
              />
            </div>

            <div className="mb-6">
              <label htmlFor="categoryIds" className="block text-sm font-medium text-gray-700 mb-2">
                Categories
              </label>

              <select
                id="categoryIds"
                name="categoryIds"
                multiple
                value={formData.categoryIds?.map(String) || []}
                onChange={handleCategoryChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                size={4}
              >
                {categories.map((category) => (
                  <option
                    key={category.id}
                    value={category.id}
                  >
                    {category.name}
                  </option>
                ))}
              </select>
              <p className="mt-2 text-xs text-gray-500">
                Hold Ctrl (or Cmd) to select multiple categories
              </p>
            </div>
          </div>

          {/* Pricing and Inventory */}
          <div className="space-y-6">
            <h3 className="text-lg font-medium border-b pb-2">Pricing & Inventory</h3>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                  Price *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="text"
                    id="price"
                    name="price"
                    value={formData.price}
                    onChange={handleChange}
                    required
                    className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-2">
                  Sale Price
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="text"
                    id="salePrice"
                    name="salePrice"
                    value={formData.salePrice}
                    onChange={handleChange}
                    className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    placeholder="0.00"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="saleStart" className="block text-sm font-medium text-gray-700 mb-2">
                  Sale Start Date
                </label>
                <input
                  type="datetime-local"
                  id="saleStart"
                  name="saleStart"
                  value={formData.saleStart}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                />
              </div>

              <div>
                <label htmlFor="saleEnd" className="block text-sm font-medium text-gray-700 mb-2">
                  Sale End Date
                </label>
                <input
                  type="datetime-local"
                  id="saleEnd"
                  name="saleEnd"
                  value={formData.saleEnd}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="stockQuantity" className="block text-sm font-medium text-gray-700 mb-2">
                  Stock Quantity
                </label>
                <input
                  type="number"
                  id="stockQuantity"
                  name="stockQuantity"
                  value={formData.stockQuantity ?? 0}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                />
              </div>

              <div>
                <label htmlFor="stockStatus" className="block text-sm font-medium text-gray-700 mb-2">
                  Stock Status
                </label>
                <select
                  id="stockStatus"
                  name="stockStatus"
                  value={formData.stockStatus}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  {Object.values(StockStatus).map((status) => (
                    <option key={status} value={status}>
                      {status.replace('_', ' ')}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="taxStatus" className="block text-sm font-medium text-gray-700 mb-2">
                  Tax Status
                </label>
                <select
                  id="taxStatus"
                  name="taxStatus"
                  value={formData.taxStatus}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  {Object.values(TaxStatus).map((status) => (
                    <option key={status} value={status}>
                      {status.replace('_', ' ')}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="taxClass" className="block text-sm font-medium text-gray-700 mb-2">
                  Tax Class
                </label>
                <select
                  id="taxClass"
                  name="taxClass"
                  value={formData.taxClass}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  {Object.values(TaxClass).map((taxClass) => (
                    <option key={taxClass} value={taxClass}>
                      {taxClass.replace('_', ' ')}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="access" className="block text-sm font-medium text-gray-700 mb-2">
                  Access Level
                </label>
                <select
                  id="access"
                  name="access"
                  value={formData.access}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  {Object.values(AccessLevel).map((level) => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>

              {formData.access === AccessLevel.PROTECTED && (
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    placeholder="Enter password for protected access"
                  />
                </div>
              )}
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.tags && formData.tags.length > 0 ? (
                  formData.tags.map((tag, index) => (
                    <div key={index} className="bg-gray-100 px-3 py-1 rounded-full flex items-center">
                      <span className="text-sm">{typeof tag === 'string' ? tag : tag.name}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-2 text-gray-500 hover:text-red-500"
                      >
                        <FiX size={14} />
                      </button>
                    </div>
                  ))
                ) : (
                  <p className="text-xs text-gray-500">No tags added yet</p>
                )}
              </div>
              <div className="flex">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleTagInputKeyDown}
                  placeholder="Enter tag and press Enter"
                  className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-main-color"
                />
                <button
                  type="button"
                  onClick={handleAddTag}
                  className="bg-main-color text-white px-4 py-2 rounded-r-md hover:bg-main-color/90 transition"
                >
                  <FiPlus />
                </button>
              </div>
              <p className="mt-2 text-xs text-gray-500">
                Add tags to help customers find your product
              </p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Images *
              </label>
              <div className="flex flex-wrap gap-4 mb-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative group">
                    <div className="w-24 h-24 border rounded overflow-hidden">
                      <img
                        src={image.url}
                        alt={`Product image ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveImage(index)}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <FiX size={14} />
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex">
                <input
                  type="url"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  placeholder="Enter image URL"
                  className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-main-color"
                />
                <button
                  type="button"
                  onClick={handleAddImage}
                  className="bg-main-color text-white px-4 py-2 rounded-r-md hover:bg-main-color/90 transition"
                >
                  <FiPlus />
                </button>
              </div>
              <p className="mt-2 text-xs text-gray-500">
                Add at least one image URL for your product
              </p>
            </div>
          </div>
        </div>

        {/* Short Description - Full Width Row */}
        <div className="mb-14 mt-8 pt-8 border-t">
          <label htmlFor="shortDescription" className="block text-sm font-medium text-gray-700 mb-2">
            Short Description
          </label>
          <div className="flex items-center gap-2">
            <div className="flex-grow">
              <RichTextEditor
                value={formData.shortDescription}
                onChange={(value) => handleRichTextChange('shortDescription', value)}
                placeholder="Enter short product description..."
                height="150px"
              />
            </div>
            {formData.shortDescription && formData.shortDescription.length > 10 && (
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-gray-100 border flex items-center justify-center relative">
                  <div className="text-sm font-bold" id="shortDescSeoScore">
                    {/* Score will be updated by JS */}
                  </div>
                </div>
              </div>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2 mb-4">A brief summary that appears in product listings</p>
          <div className="mt-4">
            <SeoEvaluator
              title={formData.name}
              content={formData.shortDescription}
              type="shortDescription"
              keyword={formData.name.split(' ')[0]}
            />
          </div>
        </div>

        {/* Full Description - Full Width Row */}
        <div className="mb-10 mt-12 pt-12 border-t">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Full Description
          </label>
          <div className="flex items-center gap-2">
            <div className="flex-grow">
              <RichTextEditor
                value={formData.description}
                onChange={(value) => handleRichTextChange('description', value)}
                placeholder="Enter detailed product description..."
                height="200px"
              />
            </div>
            {formData.description && formData.description.length > 10 && (
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-gray-100 border flex items-center justify-center relative">
                  <div className="text-sm font-bold" id="descSeoScore">
                    {/* Score will be updated by JS */}
                  </div>
                </div>
              </div>
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2 mb-4">Use the toolbar to format text with bold, italic, lists, and more</p>
          <div className="mt-6">
            <SeoEvaluator
              title={formData.name}
              content={formData.description}
              type="description"
              keyword={formData.name.split(' ')[0]}
            />
          </div>
        </div>

        {/* Additional Information (Listings) */}
        <div className="mb-10 mt-12 pt-12 border-t">
          <ListingsManager
            listings={formData.listings || []}
            onChange={handleListingsChange}
          />
        </div>

        <div className="flex justify-end space-x-4 pt-8 mt-8 border-t">
          <button
            type="button"
            onClick={handleCancel}
            className="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-3 bg-main-color text-white rounded-md hover:bg-main-color/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Saving..." : "Save Product"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;

