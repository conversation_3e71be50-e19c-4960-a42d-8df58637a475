"use client";

import { useState } from 'react';
import { FiX, FiPlus, FiAlertTriangle } from 'react-icons/fi';

// Define StockStatus enum locally since we can't import from @prisma/client
enum StockStatus {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  ON_BACKORDER = 'ON_BACKORDER'
}

interface VariantImage {
  id?: number;
  url: string;
  position: number;
  delete?: boolean;
}

interface VariantProductFormProps {
  productId: number;
  variantId: number;
  initialData?: {
    sku: string;
    price: number;
    salePrice?: number;
    saleStart?: string;
    saleEnd?: string;
    stockQuantity: number;
    stockStatus: StockStatus;
    attributeValueIds: number[];
    images: VariantImage[];
  };
  onClose: () => void;
}

export default function VariantProductForm({ productId, variantId, initialData, onClose }: VariantProductFormProps) {
  const [formData, setFormData] = useState({
    sku: initialData?.sku || '',
    price: initialData?.price || '',
    salePrice: initialData?.salePrice || '',
    saleStart: initialData?.saleStart || '',
    saleEnd: initialData?.saleEnd || '',
    stockQuantity: initialData?.stockQuantity || 1,
    stockStatus: initialData?.stockStatus || StockStatus.IN_STOCK,
    attributeValueIds: initialData?.attributeValueIds || [],
    images: initialData?.images || []
  });

  const [imageUrl, setImageUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddImage = () => {
    if (!imageUrl.trim()) {
      setError("Please enter an image URL");
      return;
    }

    const newImage: VariantImage = {
      url: imageUrl,
      position: formData.images.length
    };

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, newImage]
    }));
    setImageUrl('');
    setError(null);
  };

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...formData.images];
    const imageToRemove = updatedImages[index];

    if (imageToRemove.id) {
      // Mark existing image for deletion
      updatedImages[index] = { ...imageToRemove, delete: true };
    } else {
      // Remove new image
      updatedImages.splice(index, 1);
    }

    // Update positions
    const reorderedImages = updatedImages
      .filter(img => !img.delete)
      .map((img, idx) => ({
        ...img,
        position: idx
      }));

    setFormData(prev => ({
      ...prev,
      images: reorderedImages
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Clean up the form data to remove empty optional fields
      const cleanedFormData = {
        ...formData,
        // Convert price to number
        price: typeof formData.price === 'string' ? parseFloat(formData.price) || 0 : formData.price,
        // Only include salePrice if it's not empty
        salePrice: formData.salePrice && formData.salePrice.toString().trim() !== ''
          ? (typeof formData.salePrice === 'string' ? parseFloat(formData.salePrice) : formData.salePrice)
          : undefined,
        // Only include sale dates if they're not empty
        saleStart: formData.saleStart && formData.saleStart.trim() !== '' ? formData.saleStart : undefined,
        saleEnd: formData.saleEnd && formData.saleEnd.trim() !== '' ? formData.saleEnd : undefined,
        // Ensure stockQuantity is a number
        stockQuantity: typeof formData.stockQuantity === 'string' ? parseInt(formData.stockQuantity) || 1 : formData.stockQuantity,
      };

      // Remove undefined values
      Object.keys(cleanedFormData).forEach(key => {
        if (cleanedFormData[key as keyof typeof cleanedFormData] === undefined) {
          delete cleanedFormData[key as keyof typeof cleanedFormData];
        }
      });

      console.log('Cleaned form data:', cleanedFormData);

      const response = await fetch(`/api/store/grouped-products/${productId}/variants/${variantId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedFormData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update variant');
      }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while updating the variant');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">
        Edit Variant
      </h2>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center shadow-sm">
          <FiAlertTriangle className="mr-3 flex-shrink-0 text-red-500" size={20} />
          <span className="font-medium">{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-2">
              SKU
            </label>
            <input
              type="text"
              id="sku"
              name="sku"
              value={formData.sku}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
              Price
            </label>
            <input
              type="number"
              id="price"
              name="price"
              value={formData.price}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-2">
              Sale Price
            </label>
            <input
              type="number"
              id="salePrice"
              name="salePrice"
              value={formData.salePrice}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="stockQuantity" className="block text-sm font-medium text-gray-700 mb-2">
              Stock Quantity
            </label>
            <input
              type="number"
              id="stockQuantity"
              name="stockQuantity"
              value={formData.stockQuantity}
              onChange={handleChange}
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="stockStatus" className="block text-sm font-medium text-gray-700 mb-2">
              Stock Status
            </label>
            <select
              id="stockStatus"
              name="stockStatus"
              value={formData.stockStatus}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            >
              {Object.values(StockStatus).map((status) => (
                <option key={status} value={status}>
                  {status.replace('_', ' ')}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Variant Images</h3>

          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-2">
                Image URL
              </label>
              <input
                type="text"
                id="imageUrl"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <button
              type="button"
              onClick={handleAddImage}
              className="px-4 py-2 bg-main-color text-white rounded-md flex items-center"
            >
              <FiPlus className="mr-1" /> Add
            </button>
          </div>

          {formData.images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
              {formData.images.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={image.url}
                    alt={`Variant image ${index + 1}`}
                    className="w-full h-32 object-cover rounded-md"
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(index)}
                    className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <FiX size={16} />
                  </button>
                  <span className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    Position: {image.position}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-6 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors ${
              isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}