"use client";

import { FiChevronDown, FiChevronUp } from "react-icons/fi";
import { useState } from "react";

interface Category {
  id: number;
  name: string;
  slug: string;
  count: number;
  imageUrl: string;
}

interface CategoryFilterProps {
  categories: Category[];
  selectedCategory: number | null;
  onSelectCategory: (id: number) => void;
}

const CategoryFilter = ({ categories, selectedCategory, onSelectCategory }: CategoryFilterProps) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <div className="mb-6">
      <div
        className="flex justify-between items-center mb-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h4 className="text-sm font-medium text-gray-700">Categories</h4>
        {isExpanded ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
      </div>

      {isExpanded && (
        <div className="space-y-2">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`flex items-center justify-between py-2 px-3 rounded-lg cursor-pointer transition-colors ${
                selectedCategory === category.id
                  ? "bg-main-color/10 text-main-color font-medium"
                  : "hover:bg-gray-100"
              }`}
              onClick={() => onSelectCategory(category.id)}
            >
              <span className="text-sm">{category.name}</span>
              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                {category.count}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CategoryFilter;
