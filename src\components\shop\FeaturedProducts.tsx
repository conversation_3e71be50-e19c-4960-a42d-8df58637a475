"use client";

import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiShoppingBag, FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { fetchProductsByCategory } from "@/data/hierarchicalCategories";
import { useCartStore } from "@/hooks/useCartStore";

// Product interface
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  isNew?: boolean;
  isFeatured?: boolean;
  description?: string;
}

// Default empty array for initial render
const defaultProducts: Product[] = [];

const FeaturedProducts = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [products, setProducts] = useState<Product[]>(defaultProducts);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);

  // Fetch featured products
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // For now, we'll just fetch products from the Hair Care category (ID: 5)
        // In a real implementation, you would have a dedicated API endpoint for featured products
        const response = await fetchProductsByCategory(5, 1, 5);

        // Products already have isNew flag and salePrice from the API transformation
        const productsWithFlags = (response.data as Product[]).map((product: Product) => ({
          ...product,
          isFeatured: true // Mark all featured products
        }));

        setProducts(productsWithFlags);
      } catch (err) {
        console.error('Error fetching featured products:', err);
        setError('Failed to load featured products');
      } finally {
        setIsLoading(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10); // 10px buffer
    }
  };

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', checkScrollButtons);
      // Initial check
      checkScrollButtons();

      return () => {
        scrollContainer.removeEventListener('scroll', checkScrollButtons);
      };
    }
  }, []);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const { clientWidth } = scrollContainerRef.current;
      const scrollAmount = direction === 'left' ? -clientWidth / 2 : clientWidth / 2;
      scrollContainerRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  // Empty state
  if (products.length === 0) {
    return (
      <div className="text-center py-12 bg-gray-50 rounded-lg">
        <p className="text-gray-500">No featured products available.</p>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Scroll Buttons */}
      {canScrollLeft && (
        <button
          onClick={() => scroll('left')}
          className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-md rounded-full p-2 text-gray-700 hover:text-main-color"
          aria-label="Scroll left"
        >
          <FiChevronLeft size={24} />
        </button>
      )}

      {canScrollRight && (
        <button
          onClick={() => scroll('right')}
          className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10 bg-white shadow-md rounded-full p-2 text-gray-700 hover:text-main-color"
          aria-label="Scroll right"
        >
          <FiChevronRight size={24} />
        </button>
      )}

      {/* Products Scroll Container */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto gap-6 pb-4 scrollbar-hide -mx-4 px-4"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {products.map((product) => (
          <div
            key={product.id}
            className="flex-shrink-0 w-64 bg-white rounded-lg overflow-hidden shadow-sm border hover:shadow-md transition-all duration-300"
          >
            {/* Product Image */}
            <div className="relative h-64 w-full overflow-hidden">
              <Link href={`/shop/${product.slug}`}>
                <Image
                  src={product.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image'}
                  alt={product.name}
                  fill
                  className="object-cover transition-transform duration-500 hover:scale-105"
                />
              </Link>

              {/* Product Badges */}
              <div className="absolute top-3 left-3 flex flex-col gap-2">
                {product.isNew && (
                  <span className="bg-green-500 text-white text-xs font-medium px-2 py-1 rounded">New</span>
                )}
                {product.salePrice && (
                  <span className="bg-main-color text-white text-xs font-medium px-2 py-1 rounded">Sale</span>
                )}
              </div>

              {/* Add to Cart Button */}
              <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent translate-y-full hover:translate-y-0 transition-transform duration-300">
                <button
                  className="w-full bg-main-color text-white py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-main-color/90 transition-colors disabled:bg-gray-400"
                  disabled={!product.inStock || addingToCartId === product.id}
                  onClick={(e) => {
                    e.preventDefault(); // Prevent navigation
                    e.stopPropagation(); // Prevent event bubbling

                    if (!product.inStock) return;

                    setAddingToCartId(product.id);

                    const { addItem } = useCartStore.getState();
                    const productToAdd = {
                      id: product.id,
                      name: product.name,
                      price: product.salePrice !== null ? product.salePrice : product.price,
                      image: product.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image',
                      sku: product.sku,
                      stockQuantity: 100, // Default value as we don't have exact quantity
                      stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK',
                      variantId: null
                    };

                    addItem(productToAdd, 1);

                    // Reset the loading state after a delay
                    setTimeout(() => {
                      setAddingToCartId(null);
                    }, 800);
                  }}
                >
                  <FiShoppingBag size={16} />
                  <span>{addingToCartId === product.id ? 'Adding...' : 'Add to Cart'}</span>
                </button>
              </div>
            </div>

            {/* Product Info */}
            <div className="p-4">
              <Link href={`/shop/${product.slug}`} className="block">
                <h3 className="text-lg font-medium text-gray-800 hover:text-main-color transition-colors mb-1 truncate">
                  {product.name}
                </h3>
              </Link>

              <div
                className="text-sm text-gray-600 mb-3 line-clamp-2 product-description"
                dangerouslySetInnerHTML={{
                  __html: product.description || `${product.name} - ${product.sku}`
                }}
              />

              {/* Price */}
              <div className="flex items-center gap-2">
                {product.salePrice ? (
                  <>
                    <span className="text-lg font-medium text-main-color">${product.salePrice}</span>
                    <span className="text-sm text-gray-500 line-through">${product.price}</span>
                  </>
                ) : (
                  <span className="text-lg font-medium text-gray-800">${product.price}</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FeaturedProducts;
