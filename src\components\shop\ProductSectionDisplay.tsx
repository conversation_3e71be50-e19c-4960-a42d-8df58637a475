"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiShoppingBag, FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { ProductSection, ProductSectionItem } from "@/services/api";

interface ProductSectionDisplayProps {
  position: number;
  className?: string;
}

const ProductSectionDisplay = ({ position, className = "" }: ProductSectionDisplayProps) => {
  const [section, setSection] = useState<ProductSection | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  // Fetch the product section by position
  useEffect(() => {
    const fetchSection = async () => {
      setIsLoading(true);
      setError(null);
      try {
        console.log(`Fetching product section at position ${position}...`);
        // Add timestamp to URL to prevent browser caching
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/store/product-sections/position/${position}?t=${timestamp}`, {
          cache: 'no-store'
        });

        // Log the raw response
        console.log(`Response status for position ${position}:`, response.status, response.statusText);

        // Clone the response to read it twice (once for logging, once for processing)
        const responseClone = response.clone();
        const responseText = await responseClone.text();
        console.log(`Raw response for position ${position}:`, responseText);

        // Check if the response is empty or invalid JSON
        if (!responseText || responseText.trim() === '') {
          console.error(`Empty response received for position ${position}`);
          throw new Error('Empty response received from server');
        }

        // Parse the JSON
        let data;
        try {
          data = JSON.parse(responseText);
        } catch (jsonError) {
          console.error(`Invalid JSON in response for position ${position}:`, jsonError);
          throw new Error('Invalid JSON response');
        }

        if (!response.ok) {
          console.error(`Failed to fetch product section: ${response.status} ${response.statusText}`);
          throw new Error(`Failed to fetch product section: ${response.statusText}`);
        }

        // Log the parsed data
        console.log(`Successfully fetched product section at position ${position}:`, data);

        // Check if the data has the expected structure
        if (!data || !data.name) {
          console.warn(`Response for position ${position} is missing expected fields:`, data);
        }

        if (!data.items || !Array.isArray(data.items)) {
          console.warn(`Response for position ${position} has no items or items is not an array:`, data);
        } else if (data.items.length === 0) {
          console.warn(`Response for position ${position} has empty items array`);
        } else {
          console.log(`Section ${position} has ${data.items.length} items`);
        }

        setSection(data);
      } catch (err) {
        console.error(`Error fetching product section at position ${position}:`, err);
        setError(err instanceof Error ? err.message : "Failed to load product section");
      } finally {
        setIsLoading(false);
      }
    };

    fetchSection();

    // Set up an interval to refresh data every 30 seconds
    const intervalId = setInterval(() => {
      console.log(`Auto-refreshing product section at position ${position}...`);
      fetchSection();
    }, 30000);

    // Clean up the interval on component unmount
    return () => clearInterval(intervalId);
  }, [position]);

  // Set isVisible to true immediately without animation
  useEffect(() => {
    setIsVisible(true);
  }, []);

  // Handle navigation
  const nextSlide = () => {
    if (!section?.items) return;
    setCurrentIndex((prevIndex) =>
      prevIndex === Math.ceil((section.items?.length || 0) / 4) - 1 ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    if (!section?.items) return;
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? Math.ceil((section.items ?? []).length / 4) - 1 : prevIndex - 1
    );
  };

  // Format price with proper decimal places
  const formatPrice = (price: number | string) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toFixed(2);
  };

  // Check if a product is new (created within the last 7 days)
  const isNewProduct = (createdAt: string) => {
    if (!createdAt) return false;
    const productDate = new Date(createdAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - productDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7;
  };

  // Check if a product is on sale
  const isOnSale = (salePrice: number | null) => {
    return salePrice !== null && salePrice > 0;
  };

  console.log(`Rendering ProductSectionDisplay for position ${position}:`, {
    isLoading,
    error,
    section,
    hasItems: section?.items?.length ?? 0 > 0,
    isVisible
  });

  if (isLoading) {
    console.log(`Section ${position} is still loading...`);
    return (
      <div className={`flex justify-center items-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
      </div>
    );
  }

  if (error) {
    console.log(`Section ${position} has error:`, error);
    return (
      <div className={`${className} p-4 bg-red-50 border border-red-200 rounded-md`}>
        <p className="text-red-600 text-sm">Error loading section: {error}</p>
      </div>
    );
  }

  if (!section) {
    console.log(`Section ${position} has no data`);
    return null; // Don't show anything if there's no section
  }

  if (!section.items || section.items.length === 0) {
    console.log(`Section ${position} has no items`);
    return (
      <div className={`${className} p-4 bg-yellow-50 border border-yellow-200 rounded-md`}>
        <h2 className="text-xl font-medium text-gray-800 mb-2">{section.name}</h2>
        <p className="text-gray-600 text-sm">No products available in this section</p>
      </div>
    );
  }

  console.log(`Section ${position} rendering with ${section.items.length} items`);
  console.log(`Section ${position} items:`, section.items);

  // Calculate the items to display in the current slide
  const itemsPerPage = 4;
  const totalPages = Math.ceil(section.items.length / itemsPerPage);
  const displayItems = section.items.slice(
    currentIndex * itemsPerPage,
    (currentIndex + 1) * itemsPerPage
  );

  return (
    <div
      ref={sectionRef}
      className={`${className}`}
    >
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-medium text-gray-800">
          {section.name}
        </h2>
        {totalPages > 1 && (
          <div className="flex space-x-2">
            <button
              onClick={prevSlide}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              aria-label="Previous products"
            >
              <FiChevronLeft size={20} />
            </button>
            <button
              onClick={nextSlide}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
              aria-label="Next products"
            >
              <FiChevronRight size={20} />
            </button>
          </div>
        )}
      </div>

      <div className="relative">
        {/* Left navigation arrow - visible when not on first page */}
        {totalPages > 1 && currentIndex > 0 && (
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10 bg-white shadow-md rounded-full p-2 text-gray-700 hover:text-main-color"
            aria-label="Previous products"
          >
            <FiChevronLeft size={24} />
          </button>
        )}

        {/* Right navigation arrow - visible when not on last page */}
        {totalPages > 1 && currentIndex < totalPages - 1 && (
          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10 bg-white shadow-md rounded-full p-2 text-gray-700 hover:text-main-color"
            aria-label="Next products"
          >
            <FiChevronRight size={24} />
          </button>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {displayItems.map((item) => (
            <Link
              href={`/shop/${item.product.name.toLowerCase().replace(/\s+/g, '-')}`}
              key={item.id}
              className="group bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col h-full"
            >
              <div className="relative h-64 overflow-hidden bg-gray-100">
                {item.product.imageUrl ? (
                  <Image
                    src={item.product.imageUrl}
                    alt={item.product.name}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    unoptimized={true}
                    onError={() => console.error(`Failed to load image for product: ${item.product.name}`)}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    No image available
                  </div>
                )}

                {/* Product tags */}
                <div className="absolute top-2 left-2 flex flex-col gap-1">
                  {isNewProduct(item.product.createdAt || '') && (
                    <span className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
                      NEW
                    </span>
                  )}
                  {isOnSale(Number(item.product.salePrice)) && (
                    <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                      SALE
                    </span>
                  )}
                </div>

                {/* Quick add to cart button */}
                <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-90 py-2 px-3 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                  <button className="w-full flex items-center justify-center space-x-2 bg-main-color text-white py-2 rounded-md hover:bg-main-color/90 transition-colors">
                    <FiShoppingBag />
                    <span>Add to Cart</span>
                  </button>
                </div>
              </div>

              <div className="p-4 flex flex-col flex-grow">
                <h3 className="font-medium text-gray-900 mb-1 line-clamp-2">{item.product.name}</h3>

                <div className="mt-auto pt-2 flex items-center">
                  {item.product.salePrice ? (
                    <>
                      <span className="font-bold text-main-color">${formatPrice(item.product.salePrice)}</span>
                      <span className="ml-2 text-sm text-gray-500 line-through">${formatPrice(item.product.price)}</span>
                    </>
                  ) : (
                    <span className="font-bold text-main-color">${formatPrice(item.product.price)}</span>
                  )}

                  <div className="ml-auto">
                    <span className={`text-xs px-2 py-1 rounded ${
                      item.product.stockStatus === 'IN_STOCK'
                        ? 'bg-green-100 text-green-800'
                        : item.product.stockStatus === 'ON_BACKORDER'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.product.stockStatus === 'IN_STOCK'
                        ? 'In Stock'
                        : item.product.stockStatus === 'ON_BACKORDER'
                        ? 'Backorder'
                        : 'Out of Stock'}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Pagination dots for mobile */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6 space-x-2">
          {Array.from({ length: totalPages }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full ${
                currentIndex === index ? 'bg-main-color' : 'bg-gray-300'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductSectionDisplay;







