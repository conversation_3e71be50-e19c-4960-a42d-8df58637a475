"use client";

import { useState } from "react";
import Image from "next/image";
import { FiSearch } from "react-icons/fi";

const ShopHero = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Will be implemented with real API later
    console.log("Searching for:", searchTerm);
  };

  return (
    <div className="relative h-[500px] w-full overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=1200&auto=format&fit=crop"
          alt="Organic skincare products"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="relative h-full px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 flex flex-col justify-center">
        <div className="max-w-2xl">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
            Pure Organic Beauty
          </h1>
          <p className="text-lg md:text-xl text-white/90 mb-8">
            Discover our collection of natural, organic skincare products that nourish your skin and respect the environment.
          </p>

          {/* Search Bar */}
          <form onSubmit={handleSearch} className="relative max-w-md">
            <input
              type="text"
              placeholder="Search for products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full py-3 px-5 pr-12 rounded-full bg-white/90 backdrop-blur-sm focus:bg-white text-gray-800 focus:outline-none focus:ring-2 focus:ring-main-color/50 transition-all duration-300"
            />
            <button
              type="submit"
              className="absolute right-1 top-1 bg-main-color text-white p-2 rounded-full hover:bg-main-color/90 transition-colors"
            >
              <FiSearch size={20} />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ShopHero;
