import { create } from "zustand";

type CartItem = {
  id: string;
  productId: string;
  variantId: string | null;
  quantity: number;
  name: string;
  price: number;
  image: string;
  sku: string;
  stockQuantity: number;
  stockStatus: string;
};

type CartState = {
  items: CartItem[];
  isLoading: boolean;
  counter: number;
  totalPrice: number;
  getCart: () => void;
  addItem: (
    product: {
      id: string | number;
      name: string;
      price: number;
      image: string;
      sku: string;
      stockQuantity: number;
      stockStatus: string;
      variantId?: string | number | null;
    },
    quantity: number
  ) => void;
  removeItem: (itemId: string) => void;
  updateItemQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  calculateTotals: () => void;
};

// Helper to get cart from localStorage
const getStoredCart = (): CartItem[] => {
  if (typeof window === 'undefined') return [];
  const stored = localStorage.getItem('cart');
  return stored ? JSON.parse(stored) : [];
};

// Helper to save cart to localStorage
const saveCartToStorage = (items: CartItem[]) => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('cart', JSON.stringify(items));
};

// Helper to calculate cart totals
const calculateCartTotals = (items: CartItem[]) => {
  const totalPrice = items.reduce((total, item) => total + (item.price * item.quantity), 0);
  return {
    totalPrice,
    counter: items.reduce((count, item) => count + item.quantity, 0)
  };
};

export const useCartStore = create<CartState>((set, get) => ({
  items: [],
  isLoading: false,
  counter: 0,
  totalPrice: 0,

  getCart: () => {
    const items = getStoredCart();
    const { totalPrice, counter } = calculateCartTotals(items);
    set({
      items,
      counter,
      totalPrice,
      isLoading: false,
    });
  },

  calculateTotals: () => {
    const { items } = get();
    const { totalPrice, counter } = calculateCartTotals(items);
    set({ totalPrice, counter });
  },

  addItem: async (product, quantity) => {
    set({ isLoading: true });

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    set(state => {
      const newItems = [...state.items];
      const productId = product.id.toString();
      const variantId = product.variantId ? product.variantId.toString() : null;

      const existingItemIndex = newItems.findIndex(
        item => item.productId === productId && item.variantId === variantId
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item already exists
        newItems[existingItemIndex].quantity += quantity;
      } else {
        // Add new item
        newItems.push({
          id: Math.random().toString(36).substr(2, 9),
          productId,
          variantId,
          quantity,
          name: product.name,
          price: product.price,
          image: product.image,
          sku: product.sku,
          stockQuantity: product.stockQuantity,
          stockStatus: product.stockStatus
        });
      }

      saveCartToStorage(newItems);

      const { totalPrice, counter } = calculateCartTotals(newItems);

      return {
        items: newItems,
        counter,
        totalPrice,
        isLoading: false,
      };
    });
  },

  updateItemQuantity: async (itemId, quantity) => {
    set({ isLoading: true });

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    set(state => {
      const newItems = [...state.items];
      const itemIndex = newItems.findIndex(item => item.id === itemId);

      if (itemIndex >= 0) {
        // Ensure quantity is within valid range
        const newQuantity = Math.max(1, Math.min(quantity, newItems[itemIndex].stockQuantity));
        newItems[itemIndex].quantity = newQuantity;
      }

      saveCartToStorage(newItems);

      const { totalPrice, counter } = calculateCartTotals(newItems);

      return {
        items: newItems,
        counter,
        totalPrice,
        isLoading: false,
      };
    });
  },

  removeItem: async (itemId) => {
    set({ isLoading: true });

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));

    set(state => {
      const newItems = state.items.filter(item => item.id !== itemId);
      saveCartToStorage(newItems);

      const { totalPrice, counter } = calculateCartTotals(newItems);

      return {
        items: newItems,
        counter,
        totalPrice,
        isLoading: false,
      };
    });
  },

  clearCart: () => {
    saveCartToStorage([]);
    set({
      items: [],
      counter: 0,
      totalPrice: 0,
      isLoading: false,
    });
  },
}));

