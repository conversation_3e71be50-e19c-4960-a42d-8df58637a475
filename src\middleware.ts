import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Check for admin dashboard access
  const isAdminPath = request.nextUrl.pathname.startsWith('/admin/dashboard')
  const isAdminLoggedIn = request.cookies.has('admin-token')

  if (isAdminPath && !isAdminLoggedIn) {
    return NextResponse.redirect(new URL('/admin', request.url))
  }

  // Check for profile page access
  const isProfilePath = request.nextUrl.pathname.startsWith('/profile')
  const isUserLoggedIn = request.cookies.has('auth-token')

  if (isProfilePath && !isUserLoggedIn) {
    return NextResponse.redirect(new URL('/login', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: ['/admin/dashboard/:path*', '/profile/:path*', '/profile']
}